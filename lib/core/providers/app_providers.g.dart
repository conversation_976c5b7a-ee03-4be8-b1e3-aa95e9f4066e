// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$taskServiceHash() => r'd81772250faf580886c55902f22c3477d1f5cba1';

/// 🎯 核心服务Providers - 确保单例模式和正确的生命周期管理
/// 📋 任务服务Provider - 从Provider迁移到Riverpod
/// 🔧 修复：使用keepAlive确保服务不会被自动销毁
///
/// Copied from [taskService].
@ProviderFor(taskService)
final taskServiceProvider = Provider<TaskService>.internal(
  taskService,
  name: r'taskServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$taskServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TaskServiceRef = ProviderRef<TaskService>;
String _$mlkitServiceHash() => r'a030f166bd8b129beb77492309fcb7c469c56842';

/// 🤖 ML Kit文本识别服务Provider
///
/// Copied from [mlkitService].
@ProviderFor(mlkitService)
final mlkitServiceProvider =
    AutoDisposeProvider<MLKitTextRecognitionService>.internal(
  mlkitService,
  name: r'mlkitServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$mlkitServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MlkitServiceRef = AutoDisposeProviderRef<MLKitTextRecognitionService>;
String _$performanceOptimizerHash() =>
    r'8993b3a7abad9602ccb9388f34b0cc7e4bd92a8a';

/// ⚡ 性能优化服务Provider
///
/// Copied from [performanceOptimizer].
@ProviderFor(performanceOptimizer)
final performanceOptimizerProvider =
    AutoDisposeProvider<PerformanceOptimizer>.internal(
  performanceOptimizer,
  name: r'performanceOptimizerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$performanceOptimizerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PerformanceOptimizerRef = AutoDisposeProviderRef<PerformanceOptimizer>;
String _$appSecurityServiceHash() =>
    r'e7cdcbec5093b9d971176e0f9a21f3225d39509e';

/// 🔒 应用安全服务Provider
///
/// Copied from [appSecurityService].
@ProviderFor(appSecurityService)
final appSecurityServiceProvider =
    AutoDisposeProvider<AppSecurityService>.internal(
  appSecurityService,
  name: r'appSecurityServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$appSecurityServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AppSecurityServiceRef = AutoDisposeProviderRef<AppSecurityService>;
String _$taskNotifierHash() => r'3d65acf85e35e759cd8f79207beeacaedb6e1fba';

/// 📋 任务状态管理Provider
///
/// Copied from [TaskNotifier].
@ProviderFor(TaskNotifier)
final taskNotifierProvider =
    AutoDisposeAsyncNotifierProvider<TaskNotifier, List<TaskModel>>.internal(
  TaskNotifier.new,
  name: r'taskNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$taskNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TaskNotifier = AutoDisposeAsyncNotifier<List<TaskModel>>;
String _$currentTaskNotifierHash() =>
    r'215c116258acd683771cc1f96077cfcc6069dd56';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$CurrentTaskNotifier
    extends BuildlessAutoDisposeNotifier<TaskModel?> {
  late final String? taskId;

  TaskModel? build(
    String? taskId,
  );
}

/// 🔄 当前任务Provider - 用于任务详情页面
///
/// Copied from [CurrentTaskNotifier].
@ProviderFor(CurrentTaskNotifier)
const currentTaskNotifierProvider = CurrentTaskNotifierFamily();

/// 🔄 当前任务Provider - 用于任务详情页面
///
/// Copied from [CurrentTaskNotifier].
class CurrentTaskNotifierFamily extends Family<TaskModel?> {
  /// 🔄 当前任务Provider - 用于任务详情页面
  ///
  /// Copied from [CurrentTaskNotifier].
  const CurrentTaskNotifierFamily();

  /// 🔄 当前任务Provider - 用于任务详情页面
  ///
  /// Copied from [CurrentTaskNotifier].
  CurrentTaskNotifierProvider call(
    String? taskId,
  ) {
    return CurrentTaskNotifierProvider(
      taskId,
    );
  }

  @override
  CurrentTaskNotifierProvider getProviderOverride(
    covariant CurrentTaskNotifierProvider provider,
  ) {
    return call(
      provider.taskId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'currentTaskNotifierProvider';
}

/// 🔄 当前任务Provider - 用于任务详情页面
///
/// Copied from [CurrentTaskNotifier].
class CurrentTaskNotifierProvider
    extends AutoDisposeNotifierProviderImpl<CurrentTaskNotifier, TaskModel?> {
  /// 🔄 当前任务Provider - 用于任务详情页面
  ///
  /// Copied from [CurrentTaskNotifier].
  CurrentTaskNotifierProvider(
    String? taskId,
  ) : this._internal(
          () => CurrentTaskNotifier()..taskId = taskId,
          from: currentTaskNotifierProvider,
          name: r'currentTaskNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$currentTaskNotifierHash,
          dependencies: CurrentTaskNotifierFamily._dependencies,
          allTransitiveDependencies:
              CurrentTaskNotifierFamily._allTransitiveDependencies,
          taskId: taskId,
        );

  CurrentTaskNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.taskId,
  }) : super.internal();

  final String? taskId;

  @override
  TaskModel? runNotifierBuild(
    covariant CurrentTaskNotifier notifier,
  ) {
    return notifier.build(
      taskId,
    );
  }

  @override
  Override overrideWith(CurrentTaskNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: CurrentTaskNotifierProvider._internal(
        () => create()..taskId = taskId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        taskId: taskId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<CurrentTaskNotifier, TaskModel?>
      createElement() {
    return _CurrentTaskNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CurrentTaskNotifierProvider && other.taskId == taskId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, taskId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CurrentTaskNotifierRef on AutoDisposeNotifierProviderRef<TaskModel?> {
  /// The parameter `taskId` of this provider.
  String? get taskId;
}

class _CurrentTaskNotifierProviderElement
    extends AutoDisposeNotifierProviderElement<CurrentTaskNotifier, TaskModel?>
    with CurrentTaskNotifierRef {
  _CurrentTaskNotifierProviderElement(super.provider);

  @override
  String? get taskId => (origin as CurrentTaskNotifierProvider).taskId;
}

String _$appStateNotifierHash() => r'889f2227ef197145ff54c6ffa8f27377cda3b783';

/// 🎯 应用状态Provider - 全局应用状态管理
///
/// Copied from [AppStateNotifier].
@ProviderFor(AppStateNotifier)
final appStateNotifierProvider =
    AutoDisposeNotifierProvider<AppStateNotifier, AppState>.internal(
  AppStateNotifier.new,
  name: r'appStateNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$appStateNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AppStateNotifier = AutoDisposeNotifier<AppState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
